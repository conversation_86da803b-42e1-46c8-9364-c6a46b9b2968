{"version": 3, "file": "ChatInputProcessor.js", "sourceRoot": "", "sources": ["../../../src/terminal/components/ChatInputProcessor.ts"], "names": [], "mappings": "AAAA,qCAAqC;AACrC,MAAM,kBAAkB;IACd,SAAS,GAA4B,IAAI,GAAG,EAAE,CAAC;IAEvD,EAAE,CAAC,KAAa,EAAE,QAAkB;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,CAAC,KAAa,EAAE,GAAG,IAAW;QAChC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;CACF;AAKD,oCAAoC;AACpC,MAAM,KAAK,GAAG;IACZ,IAAI,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;IAChD,IAAI,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;IAChD,IAAI,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;IAChD,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;IAClD,GAAG,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;IAC/C,KAAK,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,IAAI,SAAS;CAClD,CAAC;AAkBF,MAAM,OAAO,kBAAmB,SAAQ,kBAAkB;IAChD,aAAa,CAAgB;IAC7B,cAAc,CAAiB;IAC/B,aAAa,CAAgB;IAC7B,OAAO,CAAwB;IAC/B,WAAW,GAAW,EAAE,CAAC;IACzB,YAAY,GAAW,CAAC,CAAC,CAAC;IAC1B,iBAAiB,GAA0B,IAAI,GAAG,EAAE,CAAC;IAE7D,YACE,aAA4B,EAC5B,cAA8B,EAC9B,aAA4B,EAC5B,UAA0C,EAAE;QAE5C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG;YACb,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,IAAI;YACxB,mBAAmB,EAAE,IAAI;YACzB,cAAc,EAAE,IAAI;YACpB,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,KAAa;QAC/B,wBAAwB;QACxB,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,CAAC,OAAO,CAAC,cAAc,cAAc,CAAC,CAAC;QAClG,CAAC;QAED,2BAA2B;QAC3B,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAErC,qBAAqB;QACrB,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,EAAE;gBACX,cAAc,EAAE,KAAK;aACtB,CAAC;QACJ,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3F,OAAO,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QACnD,CAAC;QAED,6BAA6B;QAC7B,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,KAAa;QACvC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;QAC/D,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE5B,OAAO;YACL,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,cAAc,EAAE,IAAI;YACpB,OAAO;YACP,IAAI;YACJ,QAAQ,EAAE;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,MAAM;aACf;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAa;QAClC,OAAO;YACL,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,cAAc,EAAE,KAAK;YACrB,QAAQ,EAAE;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM;aACrC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,SAAwB;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QACzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC1E,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IACzE,CAAC;IAED;;OAEG;IACI,0BAA0B,CAAC,KAAa;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACrC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,8BAA8B;QAC9B,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YAC1E,WAAW,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;QACxC,CAAC;QAED,8BAA8B;QAC9B,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAClE,WAAW,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;QAExC,6BAA6B;QAC7B,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,KAAa;QAC9C,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAErC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QAC/C,CAAC;QAED,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC1D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAEvC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;YAEtC,0CAA0C;YAC1C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClF,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5B,CAAC;YAED,oDAAoD;YACpD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAEvD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACxC,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;oBAChC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;wBAC7E,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC7D,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC/B,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,MAAM,iBAAiB,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAExD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,KAAa;QACjC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,sBAAsB;QAC3B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,KAAa;QAChC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,2CAA2C;QAC3C,MAAM,iBAAiB,GAAG;YACxB,eAAe,EAAE,WAAW;YAC5B,WAAW,EAAE,UAAU;YACvB,gBAAgB,EAAE,0BAA0B;YAC5C,MAAM,EAAE,oBAAoB;YAC5B,oBAAoB,EAAE,oBAAoB;YAC1C,eAAe,EAAE,YAAY;SAC9B,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC,2CAA2C,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC9C,CAAC;QAED,kCAAkC;QAClC,IAAI,kCAAkC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACnD,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACxD,CAAC;QAED,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC;YAC9B,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,KAAqB;QAChD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC;QAClD,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,WAAW,CAAC,CAAC;QAEpD,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,GAAG,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;QAClD,CAAC;QAED,OAAO,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,aAAa;QAMlB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;QAE5E,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CACtC,CAAC,MAAM,CAAC;QAET,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC;QACxD,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CACrD,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CACtC,CAAC;QAEF,OAAO;YACL,WAAW,EAAE,YAAY,CAAC,MAAM;YAChC,aAAa,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1F,YAAY;YACZ,YAAY;SACb,CAAC;IACJ,CAAC;CACF"}