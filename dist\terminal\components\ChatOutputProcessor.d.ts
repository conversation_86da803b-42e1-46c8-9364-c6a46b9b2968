declare class SimpleEventEmitter {
    private listeners;
    on(event: string, listener: Function): void;
    emit(event: string, ...args: any[]): void;
}
import { ConfigManager } from '../../config/ConfigManager.js';
import { LLMResponse, LLMStreamChunk, ToolCall } from '../../types/index.js';
export interface OutputProcessorOptions {
    enableStreaming: boolean;
    enableFormatting: boolean;
    enableTimestamps: boolean;
    maxOutputLength: number;
    streamingDelay: number;
}
export interface ProcessedOutput {
    type: 'response' | 'tool_call' | 'error' | 'system';
    content: string;
    formattedContent: string;
    metadata: {
        timestamp: Date;
        source: string;
        length: number;
        hasToolCalls: boolean;
        toolCalls?: ToolCall[];
        executionTime?: number;
    };
}
export declare class ChatOutputProcessor extends SimpleEventEmitter {
    private configManager;
    private options;
    private streamBuffer;
    private isStreaming;
    private streamStartTime;
    constructor(configManager: ConfigManager, options?: Partial<OutputProcessorOptions>);
    /**
     * Process LLM response and format for display
     */
    processResponse(response: LLMResponse): ProcessedOutput;
    /**
     * Process streaming response chunk
     */
    processStreamChunk(chunk: LLMStreamChunk): void;
    /**
     * Start streaming mode
     */
    private startStreaming;
    /**
     * Finish streaming and process final result
     */
    private finishStreaming;
    /**
     * Format response for display
     */
    private formatResponse;
    /**
     * Format code blocks with syntax highlighting
     */
    private formatCodeBlocks;
    /**
     * Format tool calls for display
     */
    private formatToolCalls;
    /**
     * Process error message
     */
    processError(error: Error, context?: string): ProcessedOutput;
    /**
     * Format error message
     */
    private formatError;
    /**
     * Process system message
     */
    processSystemMessage(message: string, type?: 'info' | 'warning' | 'success'): ProcessedOutput;
    /**
     * Format system message
     */
    private formatSystemMessage;
    /**
     * Get plain text version of formatted content
     */
    getPlainText(formattedContent: string): string;
    /**
     * Truncate content if it exceeds maximum length
     */
    truncateContent(content: string): string;
    /**
     * Get streaming status
     */
    getStreamingStatus(): {
        isStreaming: boolean;
        bufferLength: number;
        elapsedTime: number;
    };
    /**
     * Clear stream buffer
     */
    clearStreamBuffer(): void;
}
export {};
//# sourceMappingURL=ChatOutputProcessor.d.ts.map