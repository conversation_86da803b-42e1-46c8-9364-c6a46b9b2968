declare class SimpleEventEmitter {
    private listeners;
    on(event: string, listener: Function): void;
    emit(event: string, ...args: any[]): void;
}
import { ConfigManager } from '../../config/ConfigManager.js';
import { MessageHistory } from './MessageHistory.js';
import { SlashCommands } from './SlashCommands.js';
export interface InputProcessorOptions {
    enableHistory: boolean;
    enableAutoComplete: boolean;
    enableSlashCommands: boolean;
    maxInputLength: number;
}
export interface ProcessedInput {
    type: 'message' | 'command' | 'empty';
    content: string;
    isSlashCommand: boolean;
    command?: string;
    args?: string[];
    metadata?: Record<string, any>;
}
export declare class ChatInputProcessor extends SimpleEventEmitter {
    private configManager;
    private messageHistory;
    private slashCommands;
    private options;
    private inputBuffer;
    private historyIndex;
    private autoCompleteCache;
    constructor(configManager: ConfigManager, messageHistory: MessageHistory, slashCommands: SlashCommands, options?: Partial<InputProcessorOptions>);
    /**
     * Process user input and return structured result
     */
    processInput(input: string): ProcessedInput;
    /**
     * Process slash command input
     */
    private processSlashCommand;
    /**
     * Process regular message input
     */
    private processMessage;
    /**
     * Handle history navigation
     */
    navigateHistory(direction: 'up' | 'down'): string | null;
    /**
     * Get auto-complete suggestions
     */
    getAutoCompleteSuggestions(input: string): string[];
    /**
     * Get suggestions based on message history
     */
    private getHistoryBasedSuggestions;
    /**
     * Set input buffer for history navigation
     */
    setInputBuffer(input: string): void;
    /**
     * Clear auto-complete cache
     */
    clearAutoCompleteCache(): void;
    /**
     * Validate input for potential security issues
     */
    validateInput(input: string): {
        isValid: boolean;
        warnings: string[];
    };
    /**
     * Format input for display
     */
    formatInputForDisplay(input: ProcessedInput): string;
    /**
     * Get input statistics
     */
    getInputStats(): {
        totalInputs: number;
        averageLength: number;
        commandCount: number;
        messageCount: number;
    };
}
export {};
//# sourceMappingURL=ChatInputProcessor.d.ts.map