// Simple chalk-like color functions
const chalk = {
    cyan: {
        bold: (text) => `\x1b[1m\x1b[36m${text}\x1b[0m`,
        fg: (text) => `\x1b[36m${text}\x1b[0m`
    },
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    gray: (text) => `\x1b[90m${text}\x1b[0m`,
    white: (text) => `\x1b[37m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`
};
// Simple inquirer replacement
const inquirer = {
    prompt: async (questions) => {
        // For now, return default values
        const result = {};
        for (const question of questions) {
            if (question.type === 'confirm') {
                result[question.name] = question.default || false;
            }
            else {
                result[question.name] = question.default || '';
            }
        }
        return result;
    }
};
// Simple EventEmitter implementation
class SimpleEventEmitter {
    listeners = new Map();
    on(event, listener) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(listener);
    }
    emit(event, ...args) {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            eventListeners.forEach(listener => listener(...args));
        }
    }
    removeListener(event, listener) {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            const index = eventListeners.indexOf(listener);
            if (index > -1) {
                eventListeners.splice(index, 1);
            }
        }
    }
}
export class ToolCallsProcessor extends SimpleEventEmitter {
    configManager;
    shellTool;
    errorHandler;
    options;
    executionQueue = [];
    activeExecutions = new Map();
    constructor(configManager, shellTool, errorHandler, options = {}) {
        super();
        this.configManager = configManager;
        this.shellTool = shellTool;
        this.errorHandler = errorHandler;
        this.options = {
            autoApprove: false,
            enableParallelExecution: false,
            maxConcurrentTools: 3,
            executionTimeout: 30000,
            requireApprovalForDangerous: true,
            ...options
        };
    }
    /**
     * Process multiple tool calls
     */
    async processToolCalls(toolCalls) {
        this.emit('processing_started', { toolCalls });
        const results = [];
        if (this.options.enableParallelExecution) {
            results.push(...await this.executeParallel(toolCalls));
        }
        else {
            results.push(...await this.executeSequential(toolCalls));
        }
        this.emit('processing_completed', { results });
        return results;
    }
    /**
     * Execute tool calls sequentially
     */
    async executeSequential(toolCalls) {
        const results = [];
        for (const toolCall of toolCalls) {
            this.emit('tool_execution_started', { toolCall });
            const result = await this.executeSingleTool(toolCall);
            results.push(result);
            this.emit('tool_execution_completed', { result });
            // Stop execution if a critical tool fails
            if (!result.success && this.isCriticalTool(toolCall)) {
                this.emit('critical_tool_failed', { toolCall, result });
                break;
            }
        }
        return results;
    }
    /**
     * Execute tool calls in parallel
     */
    async executeParallel(toolCalls) {
        const batches = this.createExecutionBatches(toolCalls);
        const results = [];
        for (const batch of batches) {
            const batchPromises = batch.map(toolCall => {
                this.emit('tool_execution_started', { toolCall });
                return this.executeSingleTool(toolCall);
            });
            const batchResults = await Promise.allSettled(batchPromises);
            for (let i = 0; i < batchResults.length; i++) {
                const settledResult = batchResults[i];
                const toolCall = batch[i];
                if (settledResult.status === 'fulfilled') {
                    results.push(settledResult.value);
                    this.emit('tool_execution_completed', { result: settledResult.value });
                }
                else {
                    const errorResult = {
                        toolCall,
                        result: null,
                        success: false,
                        executionTime: 0,
                        error: settledResult.reason.message
                    };
                    results.push(errorResult);
                    this.emit('tool_execution_failed', { toolCall, error: settledResult.reason });
                }
            }
        }
        return results;
    }
    /**
     * Execute a single tool call
     */
    async executeSingleTool(toolCall) {
        const startTime = Date.now();
        try {
            // Parse tool arguments
            let args;
            try {
                args = JSON.parse(toolCall.function.arguments);
            }
            catch (error) {
                throw new Error(`Invalid tool arguments: ${error.message}`);
            }
            // Check if approval is required
            const requiresApproval = this.requiresApproval(toolCall, args);
            let approved = !requiresApproval;
            if (requiresApproval && !this.options.autoApprove) {
                approved = await this.requestApproval(toolCall, args);
            }
            if (!approved) {
                return {
                    toolCall,
                    result: null,
                    success: false,
                    executionTime: Date.now() - startTime,
                    error: 'Tool execution denied by user',
                    requiresApproval: true,
                    approved: false
                };
            }
            // Execute the tool
            const result = await this.executeToolFunction(toolCall.function.name, args);
            return {
                toolCall,
                result,
                success: true,
                executionTime: Date.now() - startTime,
                requiresApproval,
                approved
            };
        }
        catch (error) {
            return {
                toolCall,
                result: null,
                success: false,
                executionTime: Date.now() - startTime,
                error: error.message,
                requiresApproval: false,
                approved: false
            };
        }
    }
    /**
     * Execute specific tool function
     */
    async executeToolFunction(functionName, args) {
        switch (functionName) {
            case 'execute_shell_command':
                return await this.executeShellCommand(args);
            default:
                throw new Error(`Unknown tool function: ${functionName}`);
        }
    }
    /**
     * Execute shell command tool
     */
    async executeShellCommand(args) {
        const context = {
            command: args.command,
            workingDirectory: args.working_directory || globalThis.process?.cwd?.() || '.',
            environment: args.environment || {},
            timeout: args.timeout || this.options.executionTimeout,
            requireApproval: false // Already handled at tool level
        };
        return await this.shellTool.executeCommand(context);
    }
    /**
     * Check if tool requires approval
     */
    requiresApproval(toolCall, args) {
        if (!this.options.requireApprovalForDangerous) {
            return false;
        }
        // Shell command approval logic
        if (toolCall.function.name === 'execute_shell_command') {
            return this.isDangerousCommand(args.command);
        }
        return false;
    }
    /**
     * Check if a command is dangerous and requires approval
     */
    isDangerousCommand(command) {
        const dangerousPatterns = [
            /rm\s+-rf\s+\//, // rm -rf /
            /sudo\s+rm/, // sudo rm
            />\s*\/dev\/sda/, // Writing to disk devices
            /mkfs/, // Format filesystem
            /dd\s+if=.*of=\/dev/, // Direct disk write
            /:(){ :|:& };:/, // Fork bomb
            /sudo\s+/, // Any sudo command
            /chmod\s+.*\/etc/, // Changing permissions on system files
            /chown\s+.*\/etc/, // Changing ownership of system files
            /systemctl\s+(stop|disable)/, // Stopping critical services
            /service\s+.*\s+(stop|restart)/, // Service management
            /iptables/, // Firewall changes
            /fdisk/, // Disk partitioning
            /parted/, // Disk partitioning
            /mount.*\/dev/, // Mounting devices
            /umount/, // Unmounting
            /kill\s+-9/, // Force killing processes
            /pkill/, // Killing processes by name
            /killall/, // Killing all processes
        ];
        return dangerousPatterns.some(pattern => pattern.test(command));
    }
    /**
     * Request user approval for tool execution
     */
    async requestApproval(toolCall, args) {
        console.log(chalk.yellow('\n⚠️  Tool execution requires approval'));
        console.log(chalk.cyan.fg(`Tool: ${toolCall.function.name}`));
        console.log(chalk.gray('Arguments:'));
        Object.entries(args).forEach(([key, value]) => {
            console.log(chalk.gray(`  ${key}: ${JSON.stringify(value)}`));
        });
        const { approved } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'approved',
                message: 'Do you want to execute this tool?',
                default: false
            }
        ]);
        return approved;
    }
    /**
     * Check if tool is critical (failure should stop execution)
     */
    isCriticalTool(toolCall) {
        // Define critical tools that should stop execution on failure
        const criticalTools = ['execute_shell_command'];
        return criticalTools.includes(toolCall.function.name);
    }
    /**
     * Create execution batches for parallel processing
     */
    createExecutionBatches(toolCalls) {
        const batches = [];
        const batchSize = this.options.maxConcurrentTools;
        for (let i = 0; i < toolCalls.length; i += batchSize) {
            batches.push(toolCalls.slice(i, i + batchSize));
        }
        return batches;
    }
    /**
     * Format tool execution results for display
     */
    formatResults(results) {
        let output = chalk.cyan.bold('\n🔧 Tool Execution Results:\n\n');
        results.forEach((result, index) => {
            const status = result.success ? chalk.green('✅ SUCCESS') : chalk.red('❌ FAILED');
            const time = chalk.gray(`(${result.executionTime}ms)`);
            output += `${index + 1}. ${chalk.cyan.fg(result.toolCall.function.name)} ${status} ${time}\n`;
            if (result.requiresApproval) {
                const approvalStatus = result.approved ? chalk.green('Approved') : chalk.red('Denied');
                output += `   Approval: ${approvalStatus}\n`;
            }
            if (result.error) {
                output += `   ${chalk.red('Error:')} ${result.error}\n`;
            }
            else if (result.result) {
                output += this.formatToolResult(result.toolCall.function.name, result.result);
            }
            output += '\n';
        });
        return output;
    }
    /**
     * Format specific tool result
     */
    formatToolResult(toolName, result) {
        switch (toolName) {
            case 'execute_shell_command':
                return this.formatShellResult(result);
            default:
                return `   Result: ${JSON.stringify(result, null, 2)}\n`;
        }
    }
    /**
     * Format shell command result
     */
    formatShellResult(result) {
        let output = '';
        if (result.stdout) {
            output += `   ${chalk.white('Output:')}\n${result.stdout}\n`;
        }
        if (result.stderr) {
            output += `   ${chalk.yellow('Warnings/Errors:')}\n${result.stderr}\n`;
        }
        output += `   ${chalk.gray(`Exit Code: ${result.exitCode} | Working Dir: ${result.workingDirectory}`)}\n`;
        return output;
    }
    /**
     * Get execution statistics
     */
    getExecutionStats() {
        // This would be implemented with persistent storage
        // For now, return placeholder data
        return {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageExecutionTime: 0,
            approvalRequests: 0,
            approvedRequests: 0
        };
    }
    /**
     * Update processor options
     */
    updateOptions(options) {
        this.options = { ...this.options, ...options };
        this.emit('options_updated', this.options);
    }
}
//# sourceMappingURL=ToolCallsProcessor.js.map